/**
 * 新增打印方法测试示例
 * 演示如何使用BLEToothManage中新增的两种打印方法
 */

// 导入适配器
import bleToothManage from '../adapters/BLEToothManage.js';

/**
 * 测试commitJob方式打印
 */
async function testCommitJobPrint() {
  console.log('=== 测试commitJob方式打印 ===');
  
  // 模拟模板数据
  const template = {
    "TemplateName": "50 * 30 mm",
    "Width": 50,
    "Height": 30,
    "Density": 2,
    "Gap": 3,
    "Speed": 25,
    "Copies": 2, // 打印2份
    "DrawObjects": [
      {
        "X": 2, "Y": 5, "Width": 45, "Height": 4,
        "Content": "品名：测试产品",
        "FontSize": 4, "FontName": "", "FontStyle": 1
      },
      {
        "X": 2, "Y": 13, "Width": 45, "Height": 4,
        "Content": "操作员：张三",
        "FontSize": 4, "FontName": "", "FontStyle": 1
      },
      {
        "X": 2, "Y": 21, "Width": 45, "Height": 4,
        "Content": "日期：2025-01-14",
        "FontSize": 4, "FontName": "", "FontStyle": 1
      }
    ]
  };

  try {
    const result = await bleToothManage.doPrintMatrixWithCommitJob(
      null, // textCanvas
      [template], // templates
      null, // barcodeCanvas
      (res) => {
        console.log('commitJob打印回调:', res);
      }
    );
    
    console.log('commitJob打印成功:', result);
    return true;
  } catch (error) {
    console.error('commitJob打印失败:', error);
    return false;
  }
}

/**
 * 测试print方法打印
 */
async function testPrintMethodPrint() {
  console.log('=== 测试print方法打印 ===');
  
  // 模拟模板数据
  const template = {
    "TemplateName": "40 * 30 mm",
    "Width": 40,
    "Height": 30,
    "Density": 2,
    "Gap": 3,
    "Speed": 25,
    "Copies": 3, // 打印3份
    "DrawObjects": [
      {
        "X": 2, "Y": 3, "Width": 36, "Height": 4,
        "Content": "产品编号：P001",
        "FontSize": 3, "FontName": "", "FontStyle": 1
      },
      {
        "X": 2, "Y": 10, "Width": 36, "Height": 4,
        "Content": "生产日期：2025-01-14",
        "FontSize": 3, "FontName": "", "FontStyle": 1
      },
      {
        "X": 2, "Y": 17, "Width": 36, "Height": 4,
        "Content": "质检员：李四",
        "FontSize": 3, "FontName": "", "FontStyle": 1
      },
      {
        "X": 2, "Y": 24, "Width": 36, "Height": 4,
        "Content": "批次：B20250114",
        "FontSize": 3, "FontName": "", "FontStyle": 1
      }
    ]
  };

  try {
    const result = await bleToothManage.doPrintMatrixWithPrint(
      null, // textCanvas
      [template], // templates
      null, // barcodeCanvas
      (res) => {
        console.log('print方法打印回调:', res);
      }
    );
    
    console.log('print方法打印成功:', result);
    return true;
  } catch (error) {
    console.error('print方法打印失败:', error);
    return false;
  }
}

/**
 * 运行所有测试
 */
async function runAllTests() {
  console.log('开始测试新增的打印方法...\n');
  
  try {
    // 测试commitJob方式
    const commitJobResult = await testCommitJobPrint();
    console.log(`commitJob方式测试: ${commitJobResult ? '✅ 通过' : '❌ 失败'}\n`);
    
    // 等待一段时间再测试下一个方法
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // 测试print方法
    const printMethodResult = await testPrintMethodPrint();
    console.log(`print方法测试: ${printMethodResult ? '✅ 通过' : '❌ 失败'}\n`);
    
    console.log('=== 测试总结 ===');
    console.log(`commitJob方式: ${commitJobResult ? '✅' : '❌'}`);
    console.log(`print方法: ${printMethodResult ? '✅' : '❌'}`);
    
    if (commitJobResult && printMethodResult) {
      console.log('🎉 所有测试通过！');
    } else {
      console.log('⚠️ 部分测试失败，请检查设备连接和配置');
    }
    
  } catch (error) {
    console.error('测试过程中出现异常:', error);
  }
}

/**
 * 使用说明
 */
function showUsageInstructions() {
  console.log(`
📋 新增打印方法使用说明：

1. commitJob方式打印 (doPrintMatrixWithCommitJob)
   - 参考index.js中的textPrintTest2方法
   - 使用startJob + drawText + commitJob生成预览图
   - 支持多份打印，通过printImage的copies参数实现
   - 适合需要精确控制绘制过程的场景

2. print方法打印 (doPrintMatrixWithPrint)
   - 参考index.js中的jsonPrintTest方法
   - 使用print方法一次性提交所有页面
   - 支持多份打印，通过jobPages数组实现
   - 适合批量打印和进度监控的场景

🔧 使用方式：
// 替换原有的doPrintMatrix调用
// 原来：bleToothManage.doPrintMatrix(textCanvas, templates, barcodeCanvas, callback)
// 新方法1：bleToothManage.doPrintMatrixWithCommitJob(textCanvas, templates, barcodeCanvas, callback)
// 新方法2：bleToothManage.doPrintMatrixWithPrint(textCanvas, templates, barcodeCanvas, callback)

⚠️ 注意事项：
1. 确保设备已连接后再调用打印方法
2. template.Copies参数控制打印份数
3. 两种方法的jobName规则与index.js保持一致
4. 错误处理与原有方法保持兼容
  `);
}

// 导出测试函数
export {
  testCommitJobPrint,
  testPrintMethodPrint,
  runAllTests,
  showUsageInstructions
};

// 如果直接运行此文件，显示使用说明
if (typeof window !== 'undefined') {
  showUsageInstructions();
}
